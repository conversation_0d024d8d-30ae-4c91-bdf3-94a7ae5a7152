from datetime import datetime
import xlsxwriter
from xlsxwriter.utility import xl_rowcol_to_cell

"""
Icebox Hockey - Investor Financial Model (Integrated CFS, v2.0)
Single-file builder that writes an Excel workbook with a centralized Assumptions sheet
and scenario tabs that are fully formula-driven and integrated.

v2.0 Logic Summary:
- Implemented a complete, three-section Statement of Cash Flows (CFO, CFI, CFF).
- Fully integrated the three financial statements: The BS cash balance is now driven by the CFS.
- Corrected dividend logic to be based on cash available after operations and investing activities.
- Further modularized the worksheet writing logic for improved maintainability.
- Eliminated hardcoded row numbers in favor of a dynamic row cursor for a robust layout.
"""

OUTFILE = "Icebox Hockey - Investor Financial Model (Integrated CFS, v2.0).xlsx"

# A container class to store and pass cell references between functions.
class RefStore:
    """Container class for storing cell references between worksheet functions."""
    def __init__(self):
        # Income Statement
        self.revenue = []
        self.cogs = []
        self.ebitda = []
        self.da_total = []
        self.ebit = []
        self.tax = []
        self.net_income = []

        # Schedules
        self.capex = []
        self.ppe_dep = []
        self.int_amort = []
        self.ar_end = []
        self.inv_end = []
        self.ap_end = []
        self.delta_ar = []
        self.delta_inv = []
        self.delta_prepaid = []
        self.delta_ap = []
        self.delta_accrued = []

        # Cash Flow Statement
        self.cfo = []
        self.cfi = []
        self.dividends = []
        self.cff = []
        self.net_change_in_cash = []

        # Balance Sheet
        self.cash_end = []
        self.ppe_end = []
        self.int_end = []
        self.assets_total = []
        self.liab_total = []
        self.equity_total = []
        self.le_total = []


def _write_income_statement(ws, refs, assumptions, num_format, pct_format, r_offset):
    """Writes the Income Statement and D&A schedules. Returns the next available row."""
    ws.write(r_offset, 0, f"Financial Statements - {assumptions['scn_name']}", assumptions['h1_format'])
    ws.write_row(r_offset + 2, 0, ["Line Item"] + assumptions['years'], assumptions['hdr_format'])
    r = r_offset + 3

    # --- Income Statement ---
    ws.write(r, 0, "Revenue", assumptions['subhdr_format']); r+=1
    for i in range(5):
        prev = assumptions['R0'] if i == 0 else refs.revenue[i-1]
        formula = f"={prev}*(1+{assumptions['grow'][i]})"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.revenue.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "COGS", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"={refs.revenue[i]}*(1-{assumptions['gmp'][i]})"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.cogs.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Gross Margin %", assumptions['subhdr_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"={assumptions['gmp'][i]}", pct_format)
    
    ws.write(r, 0, "SG&A % (incl. shock)", assumptions['subhdr_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"={assumptions['sga0'][i]}+{assumptions['sgak'][i]}", pct_format)
    
    ws.write(r, 0, "Operating Expenses (SG&A)", assumptions['subhdr_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"={refs.revenue[i]}*{xl_rowcol_to_cell(r-2, 1+i)}", num_format)
    
    ws.write(r, 0, "EBITDA", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"({refs.revenue[i]}-{refs.cogs[i]})-{xl_rowcol_to_cell(r-2, 1+i)}"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.ebitda.append(xl_rowcol_to_cell(r-1, 1+i))

    # --- D&A Schedule ---
    ws.write(r, 0, "Capital Expenditures", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"=IF({assumptions['grow'][i]}>={assumptions['cap_thr']}, {refs.revenue[i]}*{assumptions['cap_pct']}*{assumptions['cap_mul']}, {refs.revenue[i]}*{assumptions['cap_pct']})"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.capex.append(xl_rowcol_to_cell(r-1, 1+i))
    
    ws.write(r, 0, "Depreciation (PPE)", assumptions['subhdr_format']); r+=1
    for i in range(5):
        legacy_dep = f"({assumptions['open_ppe']}/{assumptions['ppe_life']})"
        new_dep_terms = "+".join([f"({refs.capex[j]}/{assumptions['ppe_life']})" for j in range(i + 1)]) if refs.capex else ""
        ws.write_formula(r-1, 1+i, f"={legacy_dep}+{new_dep_terms}", num_format)
        refs.ppe_dep.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Amortization (Intangibles)", assumptions['subhdr_format']); r+=1
    for i in range(5):
        terms = "+".join([f"({assumptions['INT_ADD_CELLS'][j]}/{assumptions['int_life']})" for j in range(i + 1)])
        ws.write_formula(r-1, 1+i, f"={terms}", num_format)
        refs.int_amort.append(xl_rowcol_to_cell(r-1, 1+i))
    
    ws.write(r, 0, "D&A (Total)", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"={refs.ppe_dep[i]}+{refs.int_amort[i]}"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.da_total.append(xl_rowcol_to_cell(r-1, 1+i))
    
    # --- Income Statement (Continued) ---
    ws.write(r, 0, "EBIT", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"={refs.ebitda[i]}-{refs.da_total[i]}"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.ebit.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Income Tax (30%)", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"=MAX(0, {refs.ebit[i]}*0.30)"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.tax.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Net Income", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"={refs.ebit[i]}-{refs.tax[i]}"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.net_income.append(xl_rowcol_to_cell(r-1, 1+i))
    
    return r

def _write_wc_schedules(ws, refs, assumptions, num_format, r_offset):
    """Writes Working Capital balances and deltas. Returns the next available row."""
    ws.write(r_offset, 0, f"Working Capital Schedules", assumptions['h1_format'])
    ws.write_row(r_offset + 1, 0, ["Line Item"] + assumptions['years'], assumptions['hdr_format'])
    r = r_offset + 2
    
    # WC Balances (End of Period)
    ws.write(r, 0, "Accounts Receivable (End)", assumptions['subhdr_format']); r+=1
    for i in range(5):
        ws.write_formula(r-1, 1+i, f"{refs.revenue[i]}*({assumptions['DSO'][i]}/365)", num_format)
        refs.ar_end.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Inventory (End)", assumptions['subhdr_format']); r+=1
    for i in range(5):
        ws.write_formula(r-1, 1+i, f"{refs.cogs[i]}*({assumptions['DIO'][i]}/365)", num_format)
        refs.inv_end.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Accounts Payable (End)", assumptions['subhdr_format']); r+=1
    for i in range(5):
        ws.write_formula(r-1, 1+i, f"{refs.cogs[i]}*({assumptions['DPO'][i]}/365)", num_format)
        refs.ap_end.append(xl_rowcol_to_cell(r-1, 1+i))
    
    r += 1 # Spacer row

    # WC Deltas (Change vs. Prior Period)
    ws.write(r, 0, "Δ Accounts Receivable", assumptions['subhdr_format']); r+=1
    for i in range(5):
        prev = assumptions['open_ar'] if i == 0 else refs.ar_end[i-1]
        ws.write_formula(r-1, 1+i, f"{refs.ar_end[i]}-{prev}", num_format)
        refs.delta_ar.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Δ Inventory", assumptions['subhdr_format']); r+=1
    for i in range(5):
        prev = assumptions['open_inv'] if i == 0 else refs.inv_end[i-1]
        ws.write_formula(r-1, 1+i, f"{refs.inv_end[i]}-{prev}", num_format)
        refs.delta_inv.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Δ Prepaid Expenses", assumptions['subhdr_format']); r+=1
    for i in range(5): # Prepaids are held constant in this model
        ws.write_formula(r-1, 1+i, "0", num_format)
        refs.delta_prepaid.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Δ Accounts Payable", assumptions['subhdr_format']); r+=1
    for i in range(5):
        prev = assumptions['open_ap'] if i == 0 else refs.ap_end[i-1]
        ws.write_formula(r-1, 1+i, f"{refs.ap_end[i]}-{prev}", num_format)
        refs.delta_ap.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Δ Accrued Expenses", assumptions['subhdr_format']); r+=1
    for i in range(5): # Accrued expenses are held constant
        ws.write_formula(r-1, 1+i, "0", num_format)
        refs.delta_accrued.append(xl_rowcol_to_cell(r-1, 1+i))

    return r

def _write_cash_flow_statement(ws, refs, assumptions, num_format, r_offset):
    """Writes the Statement of Cash Flows. Returns the next available row."""
    ws.write(r_offset, 0, f"Statement of Cash Flows - {assumptions['scn_name']}", assumptions['h1_format'])
    ws.write_row(r_offset + 1, 0, ["Line Item"] + assumptions['years'], assumptions['hdr_format'])
    r = r_offset + 2
    
    # --- Cash Flow from Operations (CFO) ---
    ws.write(r, 0, "Cash Flow from Operations (CFO)", assumptions['subhdr_format_l2']); r+=1
    ws.write(r, 0, "Net Income", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"={refs.net_income[i]}", num_format)

    ws.write(r, 0, "Depreciation & Amortization", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"={refs.da_total[i]}", num_format)
    
    ws.write(r, 0, "Change in Accounts Receivable", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"=-{refs.delta_ar[i]}", num_format) # Increase in Asset = Use of Cash (-)
    
    ws.write(r, 0, "Change in Inventory", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"=-{refs.delta_inv[i]}", num_format) # Increase in Asset = Use of Cash (-)
    
    ws.write(r, 0, "Change in Prepaid Expenses", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"=-{refs.delta_prepaid[i]}", num_format) # Increase in Asset = Use of Cash (-)

    ws.write(r, 0, "Change in Accounts Payable", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"={refs.delta_ap[i]}", num_format) # Increase in Liability = Source of Cash (+)
    
    ws.write(r, 0, "Change in Accrued Expenses", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"={refs.delta_accrued[i]}", num_format) # Increase in Liability = Source of Cash (+)

    ws.write(r, 0, "Net Cash from Operations", assumptions['subhdr_format']); r+=1
    for i in range(5):
        cfo_range = f"{xl_rowcol_to_cell(r-8, 1+i)}:{xl_rowcol_to_cell(r-2, 1+i)}"
        ws.write_formula(r-1, 1+i, f"=SUM({cfo_range})", num_format)
        refs.cfo.append(xl_rowcol_to_cell(r-1, 1+i))
    
    # --- Cash Flow from Investing (CFI) ---
    ws.write(r, 0, "Cash Flow from Investing (CFI)", assumptions['subhdr_format_l2']); r+=1
    ws.write(r, 0, "Capital Expenditures", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"=-{refs.capex[i]}", num_format)

    ws.write(r, 0, "Intangible Asset Additions", assumptions['text_format']); r+=1
    for i in range(5): ws.write_formula(r-1, 1+i, f"=-{assumptions['INT_ADD_CELLS'][i]}", num_format)

    ws.write(r, 0, "Net Cash from Investing", assumptions['subhdr_format']); r+=1
    for i in range(5):
        cfi_range = f"{xl_rowcol_to_cell(r-3, 1+i)}:{xl_rowcol_to_cell(r-2, 1+i)}"
        ws.write_formula(r-1, 1+i, f"=SUM({cfi_range})", num_format)
        refs.cfi.append(xl_rowcol_to_cell(r-1, 1+i))
    
    # --- Cash Flow from Financing (CFF) ---
    ws.write(r, 0, "Cash Flow from Financing (CFF)", assumptions['subhdr_format_l2']); r+=1
    
    ws.write(r, 0, "Dividends Paid", assumptions['text_format']); r+=1
    for i in range(5):
        # Calculate cash available for dividends using a pure-formula reference to prior column cells,
        # avoiding any dependency on previously built refs lists.
        # Beg Cash (t) = Opening Cash (for Y1) OR Prior period Cash roll-forward via Net Change (for Y2+)
        if i == 0:
            begin_cash = f"{assumptions['open_cash']}"
        else:
            # Prior period ending cash is Opening Cash + sum(Net Change in Cash from Y1..Y{i})
            sum_range = f"{xl_rowcol_to_cell(r+18, 1)}:{xl_rowcol_to_cell(r+18, i)}"  # r+18 points to the 'Net Change in Cash' row created below, same section
            begin_cash = f"({assumptions['open_cash']}+SUM({sum_range}))"
        cash_available = f"({begin_cash}+{refs.cfo[i]}+{refs.cfi[i]})"
        
        # Determine dividend payment
        min_floor = f"MAX({assumptions['floor_abs']}, {assumptions['floor_pct']}*{refs.revenue[i]})"
        target_div = f"MAX(0, {assumptions['payout']}*{refs.net_income[i]})"
        max_div_payable = f"MAX(0, {cash_available}-{min_floor})"
        
        # Final dividend is the smaller of target and max payable, as a cash outflow (-)
        formula = f"=-MIN({target_div}, {max_div_payable})"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.dividends.append(xl_rowcol_to_cell(r-1, 1+i))

    ws.write(r, 0, "Net Cash from Financing", assumptions['subhdr_format']); r+=1
    for i in range(5):
        cff_range = f"{xl_rowcol_to_cell(r-2, 1+i)}:{xl_rowcol_to_cell(r-2, 1+i)}"
        ws.write_formula(r-1, 1+i, f"=SUM({cff_range})", num_format)
        refs.cff.append(xl_rowcol_to_cell(r-1, 1+i))

    # --- Cash Reconciliation ---
    r += 1 # Spacer
    ws.write(r, 0, "Net Change in Cash", assumptions['subhdr_format']); r+=1
    for i in range(5):
        formula = f"={refs.cfo[i]}+{refs.cfi[i]}+{refs.cff[i]}"
        ws.write_formula(r-1, 1+i, formula, num_format)
        refs.net_change_in_cash.append(xl_rowcol_to_cell(r-1, 1+i))
    # Provide an explicit "Ending Cash (from roll-forward)" row for readability/debugging
    ws.write(r, 0, "Ending Cash (roll-forward)", assumptions['text_format']); r+=1
    for i in range(5):
        if i == 0:
            end_cash = f"({assumptions['open_cash']}+{refs.net_change_in_cash[i]})"
        else:
            # Sum of Net Change rows up to current period + Opening Cash
            sum_range = f"{xl_rowcol_to_cell(r-1, 1)}:{xl_rowcol_to_cell(r-1, i)}"
            end_cash = f"({assumptions['open_cash']}+SUM({sum_range}))"
        ws.write_formula(r-1, 1+i, end_cash, num_format)
    
    return r

def _write_balance_sheet(ws, refs, assumptions, num_format, r_offset):
    """Writes the Balance Sheet. Returns the next available row."""
    ws.write(r_offset, 0, f"Balance Sheet - {assumptions['scn_name']}", assumptions['h1_format'])
    ws.write_row(r_offset + 1, 0, ["Line Item", "Y0"] + assumptions['years'], assumptions['hdr_format'])
    r = r_offset + 2

    # --- Assets ---
    ws.write(r, 0, "ASSETS", assumptions['subhdr_format_l2']); r+=1
    ws.write(r, 0, "Cash & Equivalents", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_cash']}", num_format) # Y0
    for i in range(5):
        prev_cash = assumptions['open_cash'] if i == 0 else refs.cash_end[i-1]
        ws.write_formula(r-1, 2+i, f"={prev_cash}+{refs.net_change_in_cash[i]}", num_format)
        refs.cash_end.append(xl_rowcol_to_cell(r-1, 2+i))

    ws.write(r, 0, "Accounts Receivable", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_ar']}", num_format)
    for i in range(5): ws.write_formula(r-1, 2+i, f"={refs.ar_end[i]}", num_format)

    ws.write(r, 0, "Inventory", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_inv']}", num_format)
    for i in range(5): ws.write_formula(r-1, 2+i, f"={refs.inv_end[i]}", num_format)

    ws.write(r, 0, "Prepaid Expenses", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_pre']}", num_format)
    for i in range(5): ws.write_formula(r-1, 2+i, f"={assumptions['open_pre']}", num_format)
    
    ws.write(r, 0, "Net PPE", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_ppe']}", num_format)
    for i in range(5):
        prev_ppe = assumptions['open_ppe'] if i==0 else refs.ppe_end[i-1]
        ws.write_formula(r-1, 2+i, f"={prev_ppe}+{refs.capex[i]}-{refs.ppe_dep[i]}", num_format)
        refs.ppe_end.append(xl_rowcol_to_cell(r-1, 2+i))

    ws.write(r, 0, "Intangible Assets (net)", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_intg']}", num_format)
    for i in range(5):
        prev_int = assumptions['open_intg'] if i==0 else refs.int_end[i-1]
        ws.write_formula(r-1, 2+i, f"={prev_int}+{assumptions['INT_ADD_CELLS'][i]}-{refs.int_amort[i]}", num_format)
        refs.int_end.append(xl_rowcol_to_cell(r-1, 2+i))

    ws.write(r, 0, "Total Assets", assumptions['subhdr_format']); r+=1
    for i in range(6):
        asset_range = f"{xl_rowcol_to_cell(r-7, 1+i)}:{xl_rowcol_to_cell(r-2, 1+i)}"
        ws.write_formula(r-1, 1+i, f"=SUM({asset_range})", num_format)
        refs.assets_total.append(xl_rowcol_to_cell(r-1, 1+i))

    # --- Liabilities & Equity ---
    r += 1 # Spacer
    ws.write(r, 0, "LIABILITIES & EQUITY", assumptions['subhdr_format_l2']); r+=1
    ws.write(r, 0, "Accounts Payable", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_ap']}", num_format)
    for i in range(5): ws.write_formula(r-1, 2+i, f"={refs.ap_end[i]}", num_format)

    ws.write(r, 0, "Accrued Expenses", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_accr']}", num_format)
    for i in range(5): ws.write_formula(r-1, 2+i, f"={assumptions['open_accr']}", num_format)

    ws.write(r, 0, "Total Liabilities", assumptions['subhdr_format']); r+=1
    for i in range(6):
        liab_range = f"{xl_rowcol_to_cell(r-3, 1+i)}:{xl_rowcol_to_cell(r-2, 1+i)}"
        ws.write_formula(r-1, 1+i, f"=SUM({liab_range})", num_format)
        refs.liab_total.append(xl_rowcol_to_cell(r-1, 1+i))

    r += 1 # Spacer
    ws.write(r, 0, "Paid-in Capital", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_pic']}", num_format)
    for i in range(5): ws.write_formula(r-1, 2+i, f"={assumptions['open_pic']}", num_format)
    
    ws.write(r, 0, "Retained Earnings", assumptions['text_format']); r+=1
    ws.write_formula(r-1, 1, f"={assumptions['open_re']}", num_format)
    for i in range(5):
        # Use plain cell ref for opening RE; dividends are negative values from CFF
        prev_re = assumptions['open_re'] if i == 0 else xl_rowcol_to_cell(r-1, 1+i)
        ws.write_formula(r-1, 2+i, f"={prev_re}+{refs.net_income[i]}+{refs.dividends[i]}", num_format)

    ws.write(r, 0, "Total Equity", assumptions['subhdr_format']); r+=1
    for i in range(6):
        equity_range = f"{xl_rowcol_to_cell(r-3, 1+i)}:{xl_rowcol_to_cell(r-2, 1+i)}"
        ws.write_formula(r-1, 1+i, f"=SUM({equity_range})", num_format)
        refs.equity_total.append(xl_rowcol_to_cell(r-1, 1+i))
    
    r += 1 # Spacer
    ws.write(r, 0, "Total Liabilities & Equity", assumptions['subhdr_format']); r+=1
    for i in range(6):
        ws.write_formula(r-1, 1+i, f"={refs.liab_total[i]}+{refs.equity_total[i]}", num_format)
        refs.le_total.append(xl_rowcol_to_cell(r-1, 1+i))
    
    r += 1 # Spacer
    ws.write(r, 0, "Balance Check (Assets - L&E)", assumptions['subhdr_format_l2']); r+=1
    for i in range(6):
        ws.write_formula(r-1, 1+i, f"={refs.assets_total[i]}-{refs.le_total[i]}", num_format)

    return r

def build_workbook():
    wb = xlsxwriter.Workbook(OUTFILE)

    # Formats
    title = wb.add_format({'bold': True, 'font_size': 16})
    h1 = wb.add_format({'bold': True, 'font_size': 12, 'bg_color': '#D9E1F2', 'border': 1})
    hdr = wb.add_format({'bold': True, 'bg_color': '#BDD7EE', 'border': 1, 'align': 'center'})
    subhdr = wb.add_format({'bold': True, 'bg_color': '#FCE4D6', 'border': 1})
    subhdr_l2 = wb.add_format({'bold': True, 'bg_color': '#E2EFDA', 'border': 1})
    text = wb.add_format({'border': 1})
    num = wb.add_format({'num_format': '$#,##0', 'border': 1})
    pct = wb.add_format({'num_format': '0.0%', 'border': 1})
    int_fmt = wb.add_format({'num_format': '0', 'border': 1})

    years = ["Y1","Y2","Y3","Y4","Y5"]

    # 1) Assumptions sheet (inputs + derived helper references)
    ass = wb.add_worksheet("Assumptions")
    ass.set_column("A:A", 36); ass.set_column("B:G", 16)

    # Opening balances (row block)
    ass.write_row("A1", ["Opening Balances","Value"], hdr)
    openings = [
        ("Opening Cash", 181022), ("Opening AR", 0), ("Opening Inventory", 128978),
        ("Opening Prepaids", 10000), ("Opening Net PPE", 240000), ("Opening Intangibles", 0),
        ("Opening AP", 45000), ("Opening Accrued", 15000), ("Paid-in Capital", 500000),
        ("Opening Retained Earnings", 0),
    ]
    r = 2
    for k,v in openings:
        ass.write(f"A{r}", k, subhdr); ass.write_number(r-1,1,v,num); r += 1

    # Revenue start (R0)
    ass.write("A13","Revenue Y0 (R0)", subhdr); ass.write_number("B13", 1152555, num)

    # Scenario growth vectors (%)
    ass.write("A15","Revenue Growth by Year (%)", h1)
    ass.write_row("A16", ["Scenario"]+years, hdr)
    growth = {
        "Base":[0.11,0.08,0.06,0.09,0.10], "Optimistic":[0.15,0.14,0.12,0.11,0.10],
        "Pessimistic":[0.05,0.00,0.03,0.04,0.05],
    }
    row_map = {scn: 17 + i for i, scn in enumerate(growth.keys())}
    for i, (scn, arr) in enumerate(growth.items()):
        ass.write(f"A{17+i}", scn, subhdr); ass.write_row(16+i, 1, arr, pct)

    # GM% by year
    ass.write("A21","Gross Margin % by Year", h1)
    ass.write_row("A22", ["Scenario"]+years, hdr)
    gm = {
        "Base":[0.525,0.520,0.515,0.520,0.525], "Optimistic":[0.535,0.540,0.545,0.550,0.550],
        "Pessimistic":[0.505,0.500,0.495,0.500,0.505],
    }
    gm_row_map = {scn: 23 + i for i, scn in enumerate(gm.keys())}
    for i, (scn, arr) in enumerate(gm.items()):
        ass.write(f"A{23+i}", scn, subhdr); ass.write_row(22+i, 1, arr, pct)

    # SG&A % by year (pre-shock)
    ass.write("A27","SG&A % of Revenue (pre-shock)", h1)
    ass.write_row("A28", ["Scenario"]+years, hdr)
    sgna = {
        "Base":[0.215,0.210,0.205,0.200,0.195], "Optimistic":[0.205,0.195,0.190,0.185,0.180],
        "Pessimistic":[0.215,0.215,0.210,0.210,0.205],
    }
    sgna_row_map = {scn: 29 + i for i, scn in enumerate(sgna.keys())}
    for i, (scn, arr) in enumerate(sgna.items()):
        ass.write(f"A{29+i}", scn, subhdr); ass.write_row(28+i, 1, arr, pct)

    # SG&A Shock (smoothed pessimistic; others 0)
    ass.write("A33","SG&A Shock (additive % points)", h1)
    ass.write_row("A34", ["Scenario"]+years, hdr)
    sgna_shock = {"Base":[0,0,0,0,0], "Optimistic":[0,0,0,0,0], "Pessimistic":[0.010,0.005,0,0,0]}
    shock_row_map = {scn: 35 + i for i, scn in enumerate(sgna_shock.keys())}
    for i, (scn, arr) in enumerate(sgna_shock.items()):
        ass.write(f"A{35+i}", scn, subhdr); ass.write_row(34+i, 1, arr, pct)

    # Dividend policy
    ass.write("A39","Dividend Policy", h1)
    ass.write_row("A40", ["Scenario","Payout Ratio"], hdr)
    div = {"Base":0.60,"Optimistic":0.70,"Pessimistic":0.65}
    div_row_map = {scn: 41 + i for i, scn in enumerate(div.keys())}
    for i, (scn, val) in enumerate(div.items()):
        ass.write(f"A{41+i}", scn, subhdr); ass.write_number(40+i,1,val,pct)
    ass.write("A45","Min Cash Floor (absolute $)", subhdr); ass.write_number("B45", 200000, num)
    ass.write("A46","Min Cash Floor (% of Revenue)", subhdr); ass.write_number("B46", 0.10, pct)

    # Capex and depreciation policy
    ass.write("A48","Capex & Depreciation Policy", h1)
    ass.write("A49","PPE Useful Life (years)", subhdr); ass.write_number("B49", 5, int_fmt)
    ass.write("A50","Base Capex % of Revenue", subhdr); ass.write_number("B50", 0.03, pct)
    ass.write("A51","Growth Threshold for Step-up", subhdr); ass.write_number("B51", 0.10, pct)
    ass.write("A52","Step-up Multiplier", subhdr); ass.write_number("B52", 1.5, num)

    # Intangibles
    ass.write("A54","Intangible Assets", h1)
    ass.write("A55","Intangible Asset Life (years)", subhdr); ass.write_number("B55", 5, int_fmt)
    ass.write_row("A56", ["Intangible Additions by Year"]+years, hdr)
    ass.write_row("B57", [38476,14731,7560,0,0], num)

    # WC drivers
    ass.write("A59","Working Capital Drivers", h1)
    ass.write_row("A60", ["Metric"]+years, hdr)
    wc = {"DSO":[30,31,29,30,30], "DPO":[45,44,46,45,45], "Turns":[4.2,4.1,4.3,4.2,4.2]}
    ass.write("A61","DSO (days)", subhdr); ass.write_row("B61", wc["DSO"], int_fmt)
    ass.write("A62","DPO (days)", subhdr); ass.write_row("B62", wc["DPO"], int_fmt)
    ass.write("A63","Inventory Turns (x)", subhdr); ass.write_row("B63", wc["Turns"], num)

    def write_scenario_sheet(scn_name):
        ws = wb.add_worksheet(f"{scn_name}_Scenario")
        ws.set_column("A:A", 42); ws.set_column("B:G", 16)
        
        refs = RefStore()

        A = "Assumptions"
        addr = lambda r,c: f"'{A}'!{c}{r}"
        
        assumptions = {
            'scn_name': scn_name, 'years': years,
            'h1_format': h1, 'hdr_format': hdr, 'subhdr_format': subhdr,
            'subhdr_format_l2': subhdr_l2, 'text_format': text,
            'open_cash': addr(2,"B"), 'open_ar': addr(3,"B"), 'open_inv': addr(4,"B"),
            'open_pre': addr(5,"B"), 'open_ppe': addr(6,"B"), 'open_intg': addr(7,"B"),
            'open_ap': addr(8,"B"), 'open_accr': addr(9,"B"), 'open_pic': addr(10,"B"),
            'open_re': addr(11,"B"), 'R0': addr(13,"B"),
            'grow': [addr(row_map[scn_name], chr(ord('B')+i)) for i in range(5)],
            'gmp':  [addr(gm_row_map[scn_name], chr(ord('B')+i)) for i in range(5)],
            'sga0': [addr(sgna_row_map[scn_name], chr(ord('B')+i)) for i in range(5)],
            'sgak': [addr(shock_row_map[scn_name], chr(ord('B')+i)) for i in range(5)],
            'payout': addr(div_row_map[scn_name],"B"),
            'floor_abs': addr(45,"B"), 'floor_pct': addr(46,"B"),
            'ppe_life': addr(49,"B"), 'cap_pct': addr(50,"B"), 'cap_thr': addr(51,"B"), 'cap_mul': addr(52,"B"),
            'int_life': addr(55,"B"), 'INT_ADD_CELLS': [addr(57, chr(ord('B')+i)) for i in range(5)],
            'DSO': [addr(61, chr(ord('B')+i)) for i in range(5)],
            'DPO': [addr(62, chr(ord('B')+i)) for i in range(5)],
            'DIO': [f"365/{addr(63, chr(ord('B')+i))}" for i in range(5)],
        }
        
        # Write statements sequentially, tracking the current row
        r_cursor = 0
        # 1) Income Statement
        r_cursor = _write_income_statement(ws, refs, assumptions, num, pct, r_offset=r_cursor)
        # 2) Working Capital Schedules
        r_cursor = _write_wc_schedules(ws, refs, assumptions, num, r_offset=r_cursor + 1)
        # 3) Statement of Cash Flows (compute cash flows and net change first)
        cfs_start_row = r_cursor + 2  # leave a spacer
        r_cursor_cfs_end = _write_cash_flow_statement(ws, refs, assumptions, num, r_offset=cfs_start_row)
        # 4) Balance Sheet (now that net_change_in_cash exists)
        bs_start_row = r_cursor + 1
        r_cursor = _write_balance_sheet(ws, refs, assumptions, num, r_offset=bs_start_row)

    # 2) Cover
    cover = wb.add_worksheet("Cover + Version")
    cover.set_column("A:A", 44); cover.set_column("B:B", 72)
    cover.write("A1","Icebox Hockey - Investor Financial Model (Integrated CFS, v2.0)", title)
    cover.write("A3","Prepared For", h1); cover.write("B3","Icebox Hockey", text)
    cover.write("A4","Prepared By", h1); cover.write("B4","Kilo Code (Model Builder)", text)
    cover.write("A5","Version", h1); cover.write("B5","v2.0: Added a fully integrated Statement of Cash Flows and corrected dividend logic. The model is now fully linked (BS cash driven by CFS).", text)
    cover.write("A6","Last Updated", h1); cover.write("B6", datetime.now().strftime("%Y-%m-%d"), text)

    # 3) Scenario sheets
    for scn in ["Base","Optimistic","Pessimistic"]:
        write_scenario_sheet(scn)

    wb.close()

if __name__ == "__main__":
    build_workbook()
    print(f"Successfully created: {OUTFILE}")

# Notes:
# The following enhancement suggestions were previously included as raw markdown text,
# which caused a SyntaxError when executing this module. They have been converted to
# comments for reference and future work planning.
#
# Pro Forma Financial Model Enhancement Suggestions
# DONE in v2.0
# - Cash Flow Statement Integration: Added complete cash flow statement with operating, investing, and financing activities.
# - Proper cash flow reconciliation: Implemented to drive the balance sheet cash balance.
#
# 1) Financial Modeling Enhancements
# - Advanced Financial Metrics: ROE, ROA, ROIC; DSCR if debt is added later; WC efficiency ratios; EBITDA margins/trends.
# - Sensitivity Analysis Module: Monte Carlo, tornado charts, break-even analysis, scenario probability weighting.
#
# 2) Code Architecture Improvements
# - Configuration Management: Extract assumptions to JSON/YAML; add validation/type checking; environment-specific configs.
# - Enhanced Modular Design: Consider a FinancialModel class to orchestrate building all sheets.